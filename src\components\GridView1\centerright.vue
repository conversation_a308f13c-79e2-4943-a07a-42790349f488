<template>
  <div class="table-container">
    <div class="table-header">
      <div class="header-cell">偏航角度</div>
      <div class="header-cell">时间</div>
      <div class="header-cell">状态</div>
    </div>
    <div class="table-content">
      <div class="scroll-container">
        <div class="table-body">
          <div class="table-row" v-for="(item, index) in tableData" :key="index">
            <div class="table-cell angle">{{ item.angle }}</div>
            <div class="table-cell time">{{ item.time }}</div>
            <div class="table-cell status" :class="item.statusClass">{{ item.status }}</div>
          </div>
        </div>
        <!-- 复制一份用于无缝滚动 -->
        <div class="table-body">
          <div class="table-row" v-for="(item, index) in tableData" :key="'copy-' + index">
            <div class="table-cell angle">{{ item.angle }}</div>
            <div class="table-cell time">{{ item.time }}</div>
            <div class="table-cell status" :class="item.statusClass">{{ item.status }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CenterCenter',
  data() {
    return {
      tableData: [
        {
          angle: '机舱',
          time: '2025-08-04 09:42:38',
          status: '正常',
          statusClass: 'normal'
        },
        {
          angle: '叶片',
          time: '2025-08-04 09:43:38',
          status: '正常',
          statusClass: 'normal'
        },
        {
          angle: '主轴',
          time: '2025-08-04 09:44:38',
          status: '正常',
          statusClass: 'normal'
        },
        {
          angle: '变桨系统',
          time: '2025-08-04 09:45:38',
          status: '正常',
          statusClass: 'normal'
        },
      
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.table-container {
  height: 100%;
  background: transparent;
  border-radius: 8px;
  overflow: hidden;
  
  .table-header {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    background: transparent;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    
    .header-cell {
      padding: 12px 16px;
      font-family: SourceHanSans;
      font-size: 14px;
      font-weight: bold;
      color: #ffffff;
      text-align: center;
      border-right: 1px solid rgba(255, 255, 255, 0.1);
      
      &:last-child {
        border-right: none;
      }
    }
  }
  
  .table-content {
    height: calc(100% - 48px);
    overflow: hidden;
    position: relative;
  }
  
  .scroll-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    animation: continuousScroll 25s linear infinite;
  }
  
  .table-body {
    flex-shrink: 0;
    
    .table-row {
      display: grid;
      grid-template-columns: 1fr 2fr 1fr;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      transition: background-color 0.3s ease;
      
      &:nth-child(odd) {
        background: rgba(255, 255, 255, 0.05);
      }
      
      &:hover {
        background: rgba(255, 255, 255, 0.05);
      }
      
      .table-cell {
        padding: 10px 16px;
        font-family: D-DIN;
        font-size: 13px;
        color: #ffffff;
        text-align: center;
        border-right: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        
        &:last-child {
          border-right: none;
        }
        
        &.angle {
          font-weight: bold;
          color: #fff;
          text-shadow: 0px 1px 8px rgba(255, 131, 0, 0.3);
        }
        
        &.time {
          font-family: SourceHanSans;
          font-size: 12px;
          color: #B8D4FF;
        }
        
        &.status {
          font-family: SourceHanSans;
          font-weight: bold;
          
          &.normal {
            color: #00FF88;
            text-shadow: 0px 1px 8px rgba(0, 255, 136, 0.3);
          }
          
          &.warning {
            color: #FFB800;
            text-shadow: 0px 1px 8px rgba(255, 184, 0, 0.3);
          }
          
          &.error {
            color: #FF4444;
            text-shadow: 0px 1px 8px rgba(255, 68, 68, 0.3);
          }
        }
      }
    }
  }
}

// 连续滚动动画
@keyframes continuousScroll {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}
</style>