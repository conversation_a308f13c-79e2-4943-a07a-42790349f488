<template>
  <!-- class="hover-card" 动画效果  -->
  <div class="dashboard-container">
    <img src="../assets/images/xiaobiaoti.png" alt="" class="xiaobiaoti">

    <!-- 上面6个卡片 -->
    <div class="top-cards-row">
      <div class="card-item">
        <TitleComponent title="风机运行参数" subtitle="Fan Operating Parameters">
           <topleft/>
        </TitleComponent>
      </div>

      <div class="card-item">
        <TitleComponent title="风机发电量统计" subtitle="Wind Turbine Power Generation Statistics">
          <topcenter/>
        </TitleComponent>
      </div>

      <div class="card-item">
        <TitleComponent title="近7天风机发电量分析" subtitle="Fan Parameters">
          <topright/>
        </TitleComponent>
      </div>

      <div class="card-item">
        <TitleComponent title="风机转速检测" subtitle="Fan Speed Detection">
          <centerrleft/>
        </TitleComponent>
      </div>

      <div class="card-item">
        <TitleComponent title="倾斜角度监测" subtitle="Cabin Direction Monitoring">
          <centercenter/>
        </TitleComponent>
      </div>

      <div class="card-item">
        <TitleComponent title="近7天监测" subtitle="Operation Monitoring">
          <centerright/>
        </TitleComponent>
      </div>
    </div>

    <!-- 下面2个卡片 -->
    <div class="bottom-cards-row">
      <div class="card-item">
        <TitleComponent title="近7天风机发电量" subtitle="Power Generation">
          <bottomleft/>
        </TitleComponent>
      </div>

      <div class="card-item">
        <TitleComponent title="当日风机发电量监测" subtitle="Wind Turbine Power Generation Monitoring">
          <bottomright/>
        </TitleComponent>
      </div>
    </div>
  </div>

  <Dialog v-model:visible="dialogVisible" title="碳访西伏河">
    <div class="dialog-content">
      <iframe src="https://sjcj.gxaqhbzx.com:8763/#/" class="iframe-content" frameborder="0"></iframe>
    </div>
  </Dialog>
</template>

<script setup>

import TitleComponent from '../components/common/TitleComponent.vue'
import Dialog from '../components/common/Dialog.vue'
import topleft from '../components/GridView1/topleft.vue'
import topcenter from '../components/GridView1/topcenter.vue'
import centerrleft from '../components/GridView1/centerrleft.vue'
import centercenter from '../components/GridView1/centercenter.vue'
import centerright from '../components/GridView1/centerright.vue'
import topright from '../components/GridView1/topright.vue'
import bottomleft from '../components/GridView1/bottomleft.vue'
import bottomright from '../components/GridView1/bottomright.vue'
import { ref } from 'vue'

const dialogVisible = ref(false)

</script>

<style lang="less" scoped>
.dashboard-container {
  margin-top: 88px;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none; // 不阻挡Three.js交互
  display: flex;
  flex-direction: column;
  gap: 20px;

  .xiaobiaoti {
    width: 1458px;
    height: 34px;
  }

  // 上面6个卡片的行
  .top-cards-row {
    display: grid;
    grid-template-columns: repeat(3, 460px);
    grid-template-rows: repeat(2, 265px);
    gap: 20px;
    padding-right: 40px;
    pointer-events: auto;

    .card-item {
      background: transparent;
    
      border-radius: 8px;
      overflow: hidden;
      pointer-events: auto;
    }
  }

  // 下面2个卡片的行 - 710×265px
  .bottom-cards-row {
    display: grid;
    grid-template-columns: repeat(2, 710px);
  gap: 4px;
  
    pointer-events: auto;

    .card-item {
      width: 710px;
      height: 265px;
      background: transparent;
    
      border-radius: 8px;
      overflow: hidden;
      pointer-events: auto;
    }
  }

  // 确保所有交互元素可以正常工作
  :deep(.el-button),
  :deep(.el-select),
  :deep(.el-input),
  :deep(.el-dialog),
  :deep(.chart-container),
  :deep(.data-card),
  :deep(.statistics-card) {
    pointer-events: auto;
  }
}



#close-btn {
  position: absolute;
  top: 15px !important;
  right: 22px !important;
  width: 25px !important;
  height: 25px !important;
  cursor: pointer;
}

.dialog-content {
  font-size: 33px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: PangMenZhengDao;
  color: black;
  flex-direction: column;
  background-color: #fff !important;
  height: 908px;

  .iframe-content {
    width: 100%;
    height: 100%;
  }
}
</style>
