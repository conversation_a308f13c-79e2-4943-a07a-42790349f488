<template>
    <div class="power-chart-container">
        <div ref="chartRef" class="chart"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
let chart = null
let resizeObserver = null

const initChart = async () => {
    if (!chartRef.value) return

    await nextTick()

    if (chart) {
        chart.dispose()
    }

    chart = echarts.init(chartRef.value)
    
    const option = {
        legend: {
            data: [
                {
                    name: '实际',
                    icon: 'path://M512,256m-256,0a256,256 0 1,0 512,0a256,256 0 1,0 -512,0Z',
                    itemStyle: {
                        color: '#4B96FF'
                    }
                },
                {
                    name: '预测',
                    icon: 'path://M512,256m-256,0a256,256 0 1,0 512,0a256,256 0 1,0 -512,0Z',
                    itemStyle: {
                        color: '#52C41A'
                    }
                }
            ],
            right: '5%',
            top: '8px',
            textStyle: {
                color: '#fff',
                fontSize: 11
            },
            itemWidth: 7,
            itemHeight: 7,
            itemGap: 10
        },
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(6, 30, 93, 0.9)',
            borderColor: '#4B96FF',
            textStyle: {
                color: '#fff',
                fontSize: 12
            },
            formatter: function(params) {
                let result = params[0].name + '<br/>'
                params.forEach(param => {
                    result += `<span style="color:${param.color}">${param.seriesName}</span>: ${param.value} kWh<br/>`
                })
                return result
            }
        },
        grid: {
            left: '8%',
            right: '5%',
            bottom: '15%',
            top: '20%',
            containLabel: false
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['07-29', '07-30', '07-31', '08-01', '08-02', '08-03', '08-04'],
            axisLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)',
                    width: 1
                }
            },
            axisLabel: {
                color: '#fff',
                fontSize: 12,
                margin: 10
            },
            axisTick: {
                show: false
            },
            splitLine: {
                show: false
            }
        },
        yAxis: {
            type: 'value',
            name: '单位:kWh',
            nameTextStyle: {
                color: '#fff',
                fontSize: 12,
                fontWeight: 'normal',
                padding: [0, 0, 0, 10]
            },
            min: 0,
            max: 40,
            interval: 10,
            axisLine: {
                show: false
            },
            axisLabel: {
                color: '#fff',
                fontSize: 12,
                margin: 10
            },
            axisTick: {
                show: false
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'solid',
                    color: 'rgba(255, 255, 255, 0.1)',
                    width: 1
                }
            }
        },
        series: [
            {
                name: '实际',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                itemStyle: {
                    color: '#4B96FF',
                    borderWidth: 2,
                    borderColor: '#fff'
                },
                lineStyle: {
                    width: 3,
                    color: '#4B96FF'
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: 'rgba(75, 150, 255, 0.3)'
                            },
                            {
                                offset: 1,
                                color: 'rgba(75, 150, 255, 0.05)'
                            }
                        ]
                    }
                },
                data: [30, 28, 14, 16, 21, 10, 30]
            },
            {
                name: '预测',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                itemStyle: {
                    color: '#52C41A',
                    borderWidth: 2,
                    borderColor: '#fff'
                },
                lineStyle: {
                    width: 3,
                    color: '#52C41A'
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: 'rgba(82, 196, 26, 0.3)'
                            },
                            {
                                offset: 1,
                                color: 'rgba(82, 196, 26, 0.05)'
                            }
                        ]
                    }
                },
                data: [32, 30, 15, 18, 22, 12, 32]
            }
        ]
    }
    
    chart.setOption(option)
}

const handleResize = () => {
    if (chart) {
        chart.resize()
    }
}

onMounted(async () => {
    await initChart()

    resizeObserver = new ResizeObserver(() => {
        handleResize()
    })

    if (chartRef.value) {
        resizeObserver.observe(chartRef.value)
    }

    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chart) {
        chart.dispose()
        chart = null
    }

    if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
    }

    window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.power-chart-container {
    width: 100%;
    height: 200px; // 固定高度，适应TitleComponent的内容区域

    .chart {
        width: 100%;
        height: 100%;
    }
}
</style>