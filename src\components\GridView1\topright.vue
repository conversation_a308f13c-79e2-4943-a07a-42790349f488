<template>
  <div class="power-generation-overview">
    
    <div class="cards-container">
      <!-- 饼图容器 -->
      <div ref="pieChart" class="pie-chart-container"></div>
      <div
        class="power-card"
        v-for="(item, index) in powerItems"
        :key="index"
        :style="{ backgroundColor: item.bgColor }"
      >
        <div class="card-icon">
          <svg-icon :icon-class="item.iconClass" />
        </div>
        <div class="power-label">{{ item.label }}</div>
        <div class="power-value">{{ item.value }}</div>
        <div class="power-unit">{{ item.unit }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'PowerGenerationOverview',
  data() {
    return {
      // 卡片数据
      powerItems: [
        {
          iconClass: 'shebei1',
          value: '62.0',
          unit: 'kWh',
          label: '尖',
          bgColor: 'rgba(255, 107, 107, 0.6)'
        },
        {
          iconClass: 'shebei2',
          value: '46.0',
          unit: 'kWh',
          label: '峰',
          bgColor: 'rgba(255, 159, 64, 0.6)'
        },
        {
          iconClass: 'shebei3',
          value: '45.0',
          unit: 'kWh',
          label: '平',
          bgColor: 'rgba(255, 217, 61, 0.6)'
        },
        {
          iconClass: 'shebei4',
          value: '64.0',
          unit: 'kWh',
          label: '谷',
          bgColor: 'rgba(107, 207, 127, 0.6)'
        }
      ],
      pieChart: null
    }
  },
  mounted() {
    this.initPieChart()
  },
  beforeUnmount() {
    if (this.pieChart) {
      this.pieChart.dispose()
    }
  },
  methods: {
    initPieChart() {
      // 初始化饼图
      this.pieChart = echarts.init(this.$refs.pieChart)

      // 准备饼图数据
      const pieData = this.powerItems.map(item => ({
        value: parseFloat(item.value),
        name: item.label,
        itemStyle: {
          color: item.bgColor.replace('0.6', '0.8') // 增加透明度
        }
      }))

      // 配置饼图选项
      const option = {
        series: [
          {
            type: 'pie',
            radius: ['35%', '60%'], // 调整内外半径，为标签留出更多空间
            center: ['50%', '50%'],
            data: pieData,
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}\n{d}%',
              fontSize: 14,
              color: '#fff',
              fontWeight: 'bold',
              distance: 8, // 标签距离饼图的距离
              lineHeight: 18
            },
            labelLine: {
              show: true,
              length: 8,
              length2: 5,
              lineStyle: {
                color: '#fff',
                width: 1
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      // 设置配置并渲染
      this.pieChart.setOption(option)

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        if (this.pieChart) {
          this.pieChart.resize()
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.power-generation-overview {
  height: 100%;
  width: 100%;

  .pie-chart-container {
    width: 280px;
    height: 220px;
    margin-right: 20px;
  }

  .cards-container {
    display: flex;
    flex-direction: row;
    gap: 8px;
    height: 100%;
    justify-content: flex-end;
    align-items: center;
    padding: 10px;

    .power-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 12px 0px;
      border-radius: 6px;
      position: relative;
      min-height: 175px;
      flex: 1;
      text-align: center;
      max-width: calc(25% - 50px);

      .card-icon {
        width: 24px;
        height: 24px;
        margin-bottom: 6px;
        display: flex;
        align-items: center;
        justify-content: center;

        svg {
          width: 18px;
          height: 18px;
          fill: #fff;
        }
      }

      .card-content {
        display: flex;
        align-items: baseline;
        justify-content: center;
        gap: 2px;
        margin-bottom: 4px;

        .power-value {
          font-family: D-DIN;
          font-size: 16px;
          font-weight: bold;
          color: #fff;
          line-height: 1;
        }

        .power-unit {
          font-family: SourceHanSans;
          font-size: 10px;
          color: #fff;
          opacity: 0.9;
        }
      }

      .power-label {
        font-family: SourceHanSans;
        font-size: 10px;
        color: #fff;
        opacity: 0.9;
      }
    }
  }
}
</style>