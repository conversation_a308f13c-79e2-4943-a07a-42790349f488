<template>
    <div class="monitoring-chart-container">
        <div ref="chartRef" class="chart"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
let chart = null
let resizeObserver = null

const initChart = async () => {
    if (!chartRef.value) return

    await nextTick()

    if (chart) {
        chart.dispose()
    }

    chart = echarts.init(chartRef.value)
    
    const option = {
       
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(6, 30, 93, 0.9)',
            borderColor: '#4B96FF',
            textStyle: {
                color: '#fff',
                fontSize: 12
            },
            formatter: function(params) {
                let result = params[0].name + '<br/>'
                params.forEach(param => {
                    const unit = param.seriesName === '发电功率' ? 'kW' : 'm/s'
                    result += `<span style="color:${param.color}">${param.seriesName}</span>: ${param.value} ${unit}<br/>`
                })
                return result
            }
        },
        grid: {
            left: '8%',
            right: '8%',
            bottom: '15%',
            top: '20%',
            containLabel: false
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
            axisLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)',
                    width: 1
                }
            },
            axisLabel: {
                color: '#fff',
                fontSize: 12,
                margin: 10
            },
            axisTick: {
                show: false
            },
            splitLine: {
                show: false
            }
        },
        yAxis: [
            {
                type: 'value',
                name: '单位:kW',
                nameTextStyle: {
                    color: '#fff',
                    fontSize: 12,
                    fontWeight: 'normal',
                    padding: [0, 0, 0, 10]
                },
                min: 0,
                max: 3,
                interval: 1,
                position: 'left',
                axisLine: {
                    show: false
                },
                axisLabel: {
                    color: '#fff',
                    fontSize: 12,
                    margin: 10
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        type: 'solid',
                        color: 'rgba(255, 255, 255, 0.1)',
                        width: 1
                    }
                }
            },
            {
                type: 'value',
                name: '单位:m/s',
                nameTextStyle: {
                    color: '#fff',
                    fontSize: 12,
                    fontWeight: 'normal',
                    padding: [0, 10, 0, 0]
                },
                min: 0,
                max: 6,
                interval: 2,
                position: 'right',
                axisLine: {
                    show: false
                },
                axisLabel: {
                    color: '#fff',
                    fontSize: 12,
                    margin: 10
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    show: false
                }
            }
        ],
        series: [
            {
                name: '发电功率',
                type: 'line',
                yAxisIndex: 0,
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                itemStyle: {
                    color: '#4B96FF',
                    borderWidth: 2,
                    borderColor: '#fff'
                },
                lineStyle: {
                    width: 3,
                    color: '#4B96FF'
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: 'rgba(75, 150, 255, 0.3)'
                            },
                            {
                                offset: 1,
                                color: 'rgba(75, 150, 255, 0.05)'
                            }
                        ]
                    }
                },
                data: [1.5, 1.8, 2.0, 2.2, 1.8, 1.6, 1.7]
            },
            {
                name: '风速',
                type: 'line',
                yAxisIndex: 1,
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                itemStyle: {
                    color: '#52C41A',
                    borderWidth: 2,
                    borderColor: '#fff'
                },
                lineStyle: {
                    width: 3,
                    color: '#52C41A'
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: 'rgba(82, 196, 26, 0.3)'
                            },
                            {
                                offset: 1,
                                color: 'rgba(82, 196, 26, 0.05)'
                            }
                        ]
                    }
                },
                data: [2.5, 2.8, 2.9, 3.0, 2.9, 2.8, 2.9]
            }
        ]
    }
    
    chart.setOption(option)
}

const handleResize = () => {
    if (chart) {
        chart.resize()
    }
}

onMounted(async () => {
    await initChart()

    resizeObserver = new ResizeObserver(() => {
        handleResize()
    })

    if (chartRef.value) {
        resizeObserver.observe(chartRef.value)
    }

    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chart) {
        chart.dispose()
        chart = null
    }

    if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
    }

    window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.monitoring-chart-container {
    width: 100%;
    height: 200px; // 固定高度，适应TitleComponent的内容区域

    .chart {
        width: 100%;
        height: 100%;
    }
}
</style>